using FluentValidation;
using ManagedAccounts.Models.Commands;

namespace ManagedAccounts.Validators
{
    /// <summary>
    /// Validator for CreateManagedAccountCommand
    /// </summary>
    public class CreateManagedAccountCommandValidator : AbstractValidator<CreateManagedAccountCommand>
    {
        public CreateManagedAccountCommandValidator()
        {
            RuleFor(x => x.CreatedBy)
                .GreaterThan(0)
                .WithMessage("CreatedBy must be a positive integer");

            RuleFor(x => x.Domicile)
                .MaximumLength(200)
                .When(x => !string.IsNullOrEmpty(x.Domicile))
                .WithMessage("Domicile cannot exceed 200 characters");

            RuleFor(x => x.InvestmentPeriodEndDate)
                .MaximumLength(100)
                .When(x => !string.IsNullOrEmpty(x.InvestmentPeriodEndDate))
                .WithMessage("Investment period end date cannot exceed 100 characters");

            RuleFor(x => x.CommitmentOutstanding)
                .GreaterThanOrEqualTo(0)
                .When(x => x.CommitmentOutstanding.HasValue)
                .WithMessage("Commitment outstanding must be a non-negative number");

            RuleFor(x => x.CommitmentOutstandingCurrency)
                .MaximumLength(10)
                .When(x => !string.IsNullOrEmpty(x.CommitmentOutstandingCurrency))
                .WithMessage("Commitment outstanding currency cannot exceed 10 characters");

            RuleFor(x => x.BaseCurrency)
                .MaximumLength(10)
                .When(x => !string.IsNullOrEmpty(x.BaseCurrency))
                .WithMessage("Base currency cannot exceed 10 characters");

            RuleFor(x => x.InvestmentManager)
                .MaximumLength(200)
                .When(x => !string.IsNullOrEmpty(x.InvestmentManager))
                .WithMessage("Investment manager cannot exceed 200 characters");

            RuleFor(x => x.Administrator)
                .MaximumLength(200)
                .When(x => !string.IsNullOrEmpty(x.Administrator))
                .WithMessage("Administrator cannot exceed 200 characters");

            RuleFor(x => x.Custodian)
                .MaximumLength(200)
                .When(x => !string.IsNullOrEmpty(x.Custodian))
                .WithMessage("Custodian cannot exceed 200 characters");

            RuleFor(x => x.LegalCounsel)
                .MaximumLength(200)
                .When(x => !string.IsNullOrEmpty(x.LegalCounsel))
                .WithMessage("Legal counsel cannot exceed 200 characters");

            RuleFor(x => x.LEI)
                .MaximumLength(50)
                .When(x => !string.IsNullOrEmpty(x.LEI))
                .WithMessage("LEI cannot exceed 50 characters");

            RuleFor(x => x.InvestmentSummary)
                .MaximumLength(6000)
                .When(x => !string.IsNullOrEmpty(x.InvestmentSummary))
                .WithMessage("Investment summary cannot exceed 6000 characters");

            // Business rules
            RuleFor(x => x)
                .Must(HaveValidDateRange)
                .When(x => x.CommencementDate.HasValue && x.MaturityDate.HasValue)
                .WithMessage("Maturity date must be after commencement date");

            RuleFor(x => x)
                .Must(HaveValidCurrencyPair)
                .When(x => x.CommitmentOutstanding.HasValue && !string.IsNullOrEmpty(x.CommitmentOutstandingCurrency))
                .WithMessage("Commitment outstanding currency must be specified when commitment outstanding amount is provided");
        }

        private static bool HaveValidDateRange(CreateManagedAccountCommand command)
        {
            return command.CommencementDate!.Value < command.MaturityDate!.Value;
        }

        private static bool HaveValidCurrencyPair(CreateManagedAccountCommand command)
        {
            return !string.IsNullOrEmpty(command.CommitmentOutstandingCurrency);
        }
    }
}
