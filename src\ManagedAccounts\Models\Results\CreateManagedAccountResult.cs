using System;

namespace ManagedAccounts.Models.Results
{
    /// <summary>
    /// Result for creating a managed account
    /// </summary>
    public class CreateManagedAccountResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// The ID of the created managed account
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="id">The created managed account ID</param>
        /// <returns>Success result</returns>
        public static CreateManagedAccountResult Success(Guid id)
        {
            return new CreateManagedAccountResult
            {
                IsSuccess = true,
                Id = id
            };
        }

        /// <summary>
        /// Creates a failure result
        /// </summary>
        /// <param name="errorMessage">Error message</param>
        /// <returns>Failure result</returns>
        public static CreateManagedAccountResult Failure(string errorMessage)
        {
            return new CreateManagedAccountResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
