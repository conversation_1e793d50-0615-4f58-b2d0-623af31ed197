using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.DBModel;
using DataAccessLayer.ManagedAccounts;
using ManagedAccounts.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ManagedAccounts.Services
{
    /// <summary>
    /// Service for managing Managed Account Details data access operations
    /// </summary>
    public class ManagedAccountDetailsService : IManagedAccountDetailsService
    {
        private readonly DBEntities _context;
        private readonly ILogger<ManagedAccountDetailsService> _logger;

        public ManagedAccountDetailsService(DBEntities context, ILogger<ManagedAccountDetailsService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Create new managed account details record
        /// </summary>
        public async Task<Guid> CreateAsync(ManagedAccountDetails details)
        {
            try
            {
                _logger.LogInformation("Creating new managed account details for managed account ID: {ManagedAccountId}", details.ManagedAccountID);

                // Set audit fields
                details.CreatedOn = DateTime.UtcNow;
                details.IsActive = true;
                details.IsDeleted = false;

                _context.ManagedAccountDetails.Add(details);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created managed account details with ID: {DetailsId} for managed account ID: {ManagedAccountId}",
                    details.ManagedAccountID, details.ManagedAccountID);

                return details.ManagedAccountID;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating managed account details for managed account ID: {ManagedAccountId}", details.ManagedAccountID);
                throw;
            }
        }

        /// <summary>
        /// Update existing managed account details record
        /// </summary>
        public async Task<bool> UpdateAsync(ManagedAccountDetails details)
        {
            try
            {
                _logger.LogInformation("Updating managed account details with ID: {DetailsId}", details.ManagedAccountID);

                var existingDetails = await _context.ManagedAccountDetails
                    .FirstOrDefaultAsync(d => d.ManagedAccountID == details.ManagedAccountID && d.IsActive && !d.IsDeleted);

                if (existingDetails == null)
                {
                    _logger.LogWarning("Managed account details with ID: {DetailsId} not found for update", details.ManagedAccountID);
                    return false;
                }

                // Update fields
                existingDetails.Domicile = details.Domicile;
                existingDetails.CommencementDate = details.CommencementDate;
                existingDetails.InvestmentPeriodEndDate = details.InvestmentPeriodEndDate;
                existingDetails.MaturityDate = details.MaturityDate;
                existingDetails.CommitmentOutstanding = details.CommitmentOutstanding;
                existingDetails.CommitmentOutstandingCurrency = details.CommitmentOutstandingCurrency;
                existingDetails.BaseCurrency = details.BaseCurrency;
                existingDetails.InvestmentManager = details.InvestmentManager;
                existingDetails.Administrator = details.Administrator;
                existingDetails.Custodian = details.Custodian;
                existingDetails.LegalCounsel = details.LegalCounsel;
                existingDetails.LEI = details.LEI;

                // Update audit fields
                existingDetails.ModifiedOn = DateTime.UtcNow;
                existingDetails.ModifiedBy = details.ModifiedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated managed account details with ID: {DetailsId}", details.ManagedAccountID);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating managed account details with ID: {DetailsId}", details.ManagedAccountID);
                throw;
            }
        }

        /// <summary>
        /// Delete managed account details record (soft delete)
        /// </summary>
        public async Task<bool> DeleteAsync(Guid detailsId, int deletedBy)
        {
            try
            {
                _logger.LogInformation("Deleting managed account details with ID: {DetailsId}", detailsId);

                var details = await _context.ManagedAccountDetails
                    .FirstOrDefaultAsync(d => d.ManagedAccountID == detailsId && d.IsActive && !d.IsDeleted);

                if (details == null)
                {
                    _logger.LogWarning("Managed account details with ID: {DetailsId} not found for deletion", detailsId);
                    return false;
                }

                // Soft delete
                details.IsDeleted = true;
                details.ModifiedOn = DateTime.UtcNow;
                details.ModifiedBy = deletedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted managed account details with ID: {DetailsId}", detailsId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting managed account details with ID: {DetailsId}", detailsId);
                throw;
            }
        }

        /// <summary>
        /// Get managed account details by ID
        /// </summary>
        public async Task<ManagedAccountDetails?> GetByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting managed account details with ID: {DetailsId}", id);

                var details = await _context.ManagedAccountDetails
                    .FirstOrDefaultAsync(d => d.ManagedAccountID == id && d.IsActive && !d.IsDeleted);

                if (details == null)
                {
                    _logger.LogWarning("Managed account details with ID: {DetailsId} not found", id);
                    return null;
                }

                _logger.LogInformation("Retrieved managed account details with ID: {DetailsId}", id);

                return details;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed account details with ID: {DetailsId}", id);
                throw;
            }
        }
    }
}
