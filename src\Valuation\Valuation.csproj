﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="ExcelNumberFormat" Version="1.1.0" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Contract\Contract.csproj" />
    <ProjectReference Include="..\DapperRepository\DapperRepository.csproj" />
    <ProjectReference Include="..\Report\Report.csproj" />
    <ProjectReference Include="..\Repository\Repository.csproj" />
    <ProjectReference Include="..\S3FileLayer\S3FileLayer.csproj" />
    <ProjectReference Include="..\XLHelper\XLHelper.csproj" />
  </ItemGroup>

</Project>
