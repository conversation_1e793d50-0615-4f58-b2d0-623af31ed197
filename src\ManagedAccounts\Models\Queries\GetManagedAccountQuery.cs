using System;
using ManagedAccounts.Models.Results;
using MediatR;

namespace ManagedAccounts.Models.Queries
{
    /// <summary>
    /// Query for getting a managed account by ID
    /// </summary>
    public class GetManagedAccountQuery :IRequest<GetManagedAccountResult>
    {
        /// <summary>
        /// The ID of the managed account to retrieve
        /// </summary>
        public Guid Id { get; set; }
    }
}
