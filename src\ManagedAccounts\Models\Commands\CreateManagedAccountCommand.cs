using ManagedAccounts.Models.Results;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace ManagedAccounts.Models.Commands
{
    /// <summary>
    /// Command for creating a new managed account
    /// </summary>
    public class CreateManagedAccountCommand : IRequest<CreateManagedAccountResult> 
    {
        /// <summary>
        /// Legal domicile of the managed account
        /// </summary>
        [StringLength(200)]
        public string? Domicile { get; set; }

        /// <summary>
        /// Date when the managed account commenced
        /// </summary>
        public DateTime? CommencementDate { get; set; }

        /// <summary>
        /// End date of the investment period (text format)
        /// </summary>
        [StringLength(100)]
        public string? InvestmentPeriodEndDate { get; set; }

        /// <summary>
        /// Maturity date of the managed account
        /// </summary>
        public DateTime? MaturityDate { get; set; }

        /// <summary>
        /// Outstanding commitment amount
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Commitment outstanding must be a positive number")]
        public decimal? CommitmentOutstanding { get; set; }

        /// <summary>
        /// Currency for the commitment outstanding amount
        /// </summary>
        [StringLength(10)]
        public string? CommitmentOutstandingCurrency { get; set; }

        /// <summary>
        /// Base currency of the managed account
        /// </summary>
        [StringLength(10)]
        public string? BaseCurrency { get; set; }

        /// <summary>
        /// Investment manager for the managed account
        /// </summary>
        [StringLength(200)]
        public string? InvestmentManager { get; set; }

        /// <summary>
        /// Administrator of the managed account
        /// </summary>
        [StringLength(200)]
        public string? Administrator { get; set; }

        /// <summary>
        /// Custodian of the managed account
        /// </summary>
        [StringLength(200)]
        public string? Custodian { get; set; }

        /// <summary>
        /// Legal counsel for the managed account
        /// </summary>
        [StringLength(200)]
        public string? LegalCounsel { get; set; }

        /// <summary>
        /// Legal Entity Identifier (alphanumeric)
        /// </summary>
        [StringLength(50)]
        public string? LEI { get; set; }

        /// <summary>
        /// Managed accounts investment summary details
        /// </summary>
        [StringLength(6000)]
        public string? InvestmentSummary { get; set; }

        /// <summary>
        /// User ID who is creating the record
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }
    }
}
