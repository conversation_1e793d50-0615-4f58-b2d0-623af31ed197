﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Fluent\PageSettings\PageDetailsConfiguration.cs" />
    <Compile Remove="Fluent\PageSettings\SubPageDetailsConfigurations.cs" />
    <Compile Remove="Fluent\PageSettings\SubPageFieldsConfiguratio.cs" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="EPPlus" Version="7.4.1" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Composition" Version="8.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Runtime.Caching" Version="9.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Fluent\PageSettings\" />
  </ItemGroup>
</Project>
